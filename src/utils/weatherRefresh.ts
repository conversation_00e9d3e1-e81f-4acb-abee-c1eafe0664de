import {
  getPersonWeather,
  getComprehensiveWeather,
  type IGetPersonWeatherResponse,
  type IComprehensiveWeatherResponse,
} from '@/apis/memory';

/**
 * 天气数据刷新事件类型
 */
export interface IWeatherRefreshEvent {
  type: 'person-weather' | 'comprehensive-weather';
  data: IGetPersonWeatherResponse | IComprehensiveWeatherResponse;
  userId: string;
  personId?: string;
}

/**
 * 天气数据刷新回调函数类型
 */
export type WeatherRefreshCallback = (event: IWeatherRefreshEvent) => void;

/**
 * 天气数据刷新管理器
 */
class WeatherRefreshManager {
  private callbacks: Set<WeatherRefreshCallback> = new Set();

  /**
   * 注册天气数据刷新回调
   */
  subscribe(callback: WeatherRefreshCallback): () => void {
    this.callbacks.add(callback);

    // 返回取消订阅函数
    return () => {
      this.callbacks.delete(callback);
    };
  }

  /**
   * 触发天气数据刷新事件
   */
  private emit(event: IWeatherRefreshEvent): void {
    this.callbacks.forEach((callback) => {
      try {
        callback(event);
      } catch (error) {
        console.error('❌ [weatherRefresh] 天气数据刷新回调执行失败:', error);
      }
    });
  }

  /**
   * 刷新人员天气数据
   */
  async refreshPersonWeather(userId: string, personId: string): Promise<void> {
    try {
      console.log('🔄 [weatherRefresh] 开始刷新人员天气数据...', { userId, personId });

      const response = await getPersonWeather({
        user_id: userId,
        person_id: personId,
      });

      console.log('📡 [weatherRefresh] 人员天气数据响应:', response);

      // 触发刷新事件
      this.emit({
        type: 'person-weather',
        data: response,
        userId,
        personId,
      });

      console.log('✅ [weatherRefresh] 人员天气数据刷新完成');
    } catch (error) {
      console.error('❌ [weatherRefresh] 刷新人员天气数据失败:', error);
      throw error;
    }
  }

  /**
   * 刷新综合天气数据
   */
  async refreshComprehensiveWeather(userId: string): Promise<void> {
    try {
      console.log('🔄 [weatherRefresh] 开始刷新综合天气数据...', { userId });

      const response = await getComprehensiveWeather({
        user_id: userId,
      });

      console.log('📡 [weatherRefresh] 综合天气数据响应:', response);

      // 触发刷新事件
      this.emit({
        type: 'comprehensive-weather',
        data: response,
        userId,
      });

      console.log('✅ [weatherRefresh] 综合天气数据刷新完成');
    } catch (error) {
      console.error('❌ [weatherRefresh] 刷新综合天气数据失败:', error);
      throw error;
    }
  }

  /**
   * 同时刷新人员天气和综合天气数据
   */
  async refreshAllWeatherData(userId: string, personId?: string): Promise<void> {
    try {
      console.log('🔄 [weatherRefresh] 开始刷新所有天气数据...', { userId, personId });

      const promises: Promise<void>[] = [this.refreshComprehensiveWeather(userId)];

      // 如果提供了personId，也刷新人员天气数据
      if (personId) {
        promises.push(this.refreshPersonWeather(userId, personId));
      }

      await Promise.all(promises);

      console.log('✅ [weatherRefresh] 所有天气数据刷新完成');
    } catch (error) {
      console.error('❌ [weatherRefresh] 刷新所有天气数据失败:', error);
      throw error;
    }
  }
}

// 创建全局实例
export const weatherRefreshManager = new WeatherRefreshManager();

/**
 * 便捷函数：在更新人员信息后刷新天气数据
 */
export const refreshWeatherAfterPersonUpdate = async (userId: string, personId: string): Promise<void> => {
  console.log('🔄 [weatherRefresh] 人员信息更新后刷新天气数据...', { userId, personId });
  await weatherRefreshManager.refreshAllWeatherData(userId, personId);
};

/**
 * 便捷函数：在聊天完成后刷新天气数据
 */
export const refreshWeatherAfterChat = async (userId: string): Promise<void> => {
  console.log('🔄 [weatherRefresh] 聊天完成后刷新天气数据...', { userId });
  await weatherRefreshManager.refreshAllWeatherData(userId);
};
