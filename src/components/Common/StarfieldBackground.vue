<template>
  <div class="starfield-container">
    <!-- 星空粒子层 -->
    <canvas ref="starCanvas" class="star-canvas"></canvas>
    <!-- 流星层 -->
    <div class="shooting-stars">
      <div v-for="i in 3" :key="i" class="shooting-star" :style="getShootingStarStyle(i)"></div>
    </div>
    <!-- 脉冲光点 -->
    <div class="pulse-dots">
      <div v-for="i in 5" :key="i" class="pulse-dot" :style="getPulseDotStyle(i)"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';

const starCanvas = ref<HTMLCanvasElement | null>(null);
let animationId: number;
let stars: Array<{
  x: number;
  y: number;
  size: number;
  speed: number;
  opacity: number;
  twinkleSpeed: number;
  twinklePhase: number;
}> = [];

// 初始化星星
const initStars = (canvas: HTMLCanvasElement, ctx: CanvasRenderingContext2D) => {
  const numStars = 150;
  stars = [];

  for (let i = 0; i < numStars; i++) {
    stars.push({
      x: Math.random() * canvas.width,
      y: Math.random() * canvas.height,
      size: Math.random() * 2 + 0.5,
      speed: Math.random() * 0.5 + 0.1,
      opacity: Math.random() * 0.8 + 0.2,
      twinkleSpeed: Math.random() * 0.02 + 0.01,
      twinklePhase: Math.random() * Math.PI * 2,
    });
  }
};

// 更新星星位置和闪烁
const updateStars = (canvas: HTMLCanvasElement) => {
  stars.forEach((star) => {
    // 缓慢移动
    star.y += star.speed;
    star.x += star.speed * 0.3;

    // 边界检查
    if (star.y > canvas.height) {
      star.y = -10;
      star.x = Math.random() * canvas.width;
    }
    if (star.x > canvas.width) {
      star.x = -10;
    }

    // 闪烁效果
    star.twinklePhase += star.twinkleSpeed;
    star.opacity = 0.3 + Math.sin(star.twinklePhase) * 0.5;
  });
};

// 绘制星星
const drawStars = (ctx: CanvasRenderingContext2D) => {
  ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

  stars.forEach((star) => {
    ctx.save();
    ctx.globalAlpha = Math.max(0, star.opacity);

    // 创建星星的发光效果
    const gradient = ctx.createRadialGradient(star.x, star.y, 0, star.x, star.y, star.size * 3);
    gradient.addColorStop(0, '#ffffff');
    gradient.addColorStop(0.3, '#87ceeb');
    gradient.addColorStop(1, 'transparent');

    ctx.fillStyle = gradient;
    ctx.beginPath();
    ctx.arc(star.x, star.y, star.size * 3, 0, Math.PI * 2);
    ctx.fill();

    // 绘制星星核心
    ctx.fillStyle = '#ffffff';
    ctx.beginPath();
    ctx.arc(star.x, star.y, star.size, 0, Math.PI * 2);
    ctx.fill();

    ctx.restore();
  });
};

// 动画循环
const animate = () => {
  if (!starCanvas.value) return;

  const canvas = starCanvas.value;
  const ctx = canvas.getContext('2d');
  if (!ctx) return;

  updateStars(canvas);
  drawStars(ctx);

  animationId = requestAnimationFrame(animate);
};

// 调整画布大小
const resizeCanvas = () => {
  if (!starCanvas.value) return;

  const canvas = starCanvas.value;
  const rect = canvas.getBoundingClientRect();
  canvas.width = rect.width * window.devicePixelRatio;
  canvas.height = rect.height * window.devicePixelRatio;

  const ctx = canvas.getContext('2d');
  if (ctx) {
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
    initStars(canvas, ctx);
  }
};

// 预定义的流星位置，避免随机数导致的重新渲染
const shootingStarPositions = [
  { top: '15%', left: '20%' },
  { top: '25%', left: '35%' },
  { top: '35%', left: '10%' },
];

// 流星样式
const getShootingStarStyle = (index: number) => {
  const delay = (index - 1) * 8; // 第一个流星立即开始，后续每8秒一个
  const duration = 3; // 流星持续3秒
  const position = shootingStarPositions[index - 1] || shootingStarPositions[0];

  return {
    animationDelay: `${delay}s`,
    animationDuration: `${duration}s`,
    top: position.top,
    left: position.left,
  };
};

// 脉冲光点样式
const getPulseDotStyle = (index: number) => {
  const delay = index * 2;

  return {
    animationDelay: `${delay}s`,
    top: `${Math.random() * 80 + 10}%`,
    left: `${Math.random() * 80 + 10}%`,
  };
};

onMounted(() => {
  if (starCanvas.value) {
    resizeCanvas();
    animate();

    window.addEventListener('resize', resizeCanvas);
  }
});

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId);
  }
  window.removeEventListener('resize', resizeCanvas);
});
</script>

<style scoped lang="scss">
.starfield-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
  overflow: hidden;
}

.star-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

// 流星效果
.shooting-stars {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.shooting-star {
  position: absolute;
  width: 2px; //流星本体长宽
  height: 2px;
  background: linear-gradient(45deg, #ffffff, #87ceeb);
  border-radius: 50%;
  animation: shooting 15s linear infinite;
  // 初始状态：在屏幕外且不可见
  transform: translateX(-100px) translateY(-100px);
  opacity: 0;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    width: 100px; //拖尾长度
    height: 2px; //拖尾线条宽度
    background: linear-gradient(to left, transparent, #ffffff, transparent);
    transform: translateY(-50%) rotateZ(45deg);
    transform-origin: right center;
  }
}

@keyframes shooting {
  0% {
    transform: translateX(-100px) translateY(-100px);
    opacity: 0;
  }
  10% {
    opacity: 0.5;
  }
  30% {
    opacity: 1;
  }
  90% {
    opacity: 0.5;
  }
  100% {
    transform: translateX(100vw) translateY(200vh);
    opacity: 0;
  }
}

// 脉冲光点
.pulse-dots {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.pulse-dot {
  position: absolute;
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, #00ffff, transparent);
  border-radius: 50%;
  animation: pulse 4s ease-in-out infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(2);
    opacity: 1;
  }
}
</style>
