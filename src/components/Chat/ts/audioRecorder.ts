// 导入录音模块
import Recorder from 'recorder-realtime';
import { getStreamAsr } from '@/apis/chat';

class RecorderService {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private recorder: any = null; // 录音实例

  private sessionId = '';

  private audioBufferIndex = 0;

  private lastBuffer: ArrayBuffer | null = null; // 存储音频 Blob 数据

  private callback;

  constructor(callback: (text: string) => void) {
    this.callback = callback;
  }

  // 发送音频数据到服务器的方法
  private async sendBlobToServer(buffer: ArrayBuffer) {
    this.lastBuffer = buffer;
    this.audioBufferIndex += 1;
    const { data } = await getStreamAsr({
      sessionId: this.sessionId,
      format: 'pcm',
      sampleRate: 16000,
      index: this.audioBufferIndex,
      data: buffer,
    });
    this.callback(data.text);
  }

  // 初始化录音机
  public initRecorder() {
    if (!Recorder.isRecordingSupported()) {
      throw new Error('录音失败，浏览器不支持录音功能');
    }

    // 创建录音机实例
    this.recorder = new Recorder({
      recordingGain: 1,
      numberOfChannels: 1,
      wavBitDepth: 16,
      format: 'pcm',
      wavSampleRate: 16000,
      streamPages: true,
      bufferLength: 4096,
    });

    // 录音开始时的回调
    this.recorder.onstart = () => {};

    // 处理录音错误的回调
    this.recorder.onstreamerror = (e: Error) => {
      throw new Error(`录音错误，${e}`);
    };

    // 获取可用数据的回调
    this.recorder.ondataavailable = async (data: { command: string; buffer: ArrayBuffer }) => {
      if (data.command === 'buffer') {
        this.lastBuffer = data.buffer;
        await this.sendBlobToServer(data.buffer); // 发送 buffer 的数据
      }
    };
  }

  // 启动录音
  public startRecording() {
    if (this.recorder) {
      this.recorder.start(); // 调用录音机的 start 方法
    }
  }

  // 停止录音并返回音频 Blob
  public async stopRecording(): Promise<Blob | null> {
    if (this.recorder) {
      this.recorder.stop(); // 停止录音
      await this.sendBlobToServer(this.lastBuffer as ArrayBuffer);
    }
    return null;
  }
}

export default RecorderService; // 导出类以供使用
