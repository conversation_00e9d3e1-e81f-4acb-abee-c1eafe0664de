import Config from '@/config/index';
// 初始化：
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
export const tracker = window.LXAnalytics('getTracker');

const lab = {
  custom: {
    fe_ai_speech_env: Config.env,
  },
};

export function setGlobalLab(customData: Record<string, unknown>) {
  lab.custom = {
    ...lab.custom,
    ...customData,
  };
}

export function getGlobalLab(customData?: Record<string, unknown>) {
  if (customData) {
    return {
      custom: {
        ...lab.custom,
        ...customData,
      },
    };
  }
  return lab;
}
