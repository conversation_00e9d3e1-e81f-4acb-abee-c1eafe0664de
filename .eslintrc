/**
 * https://eslint.org/docs/user-guide/configuring
 */
{
  "root": true,
  "parser": "vue-eslint-parser",
  "parserOptions": {
    "parser": "@typescript-eslint/parser",
    "sourceType": "module",
    "ecmaVersion": "latest"
  },
  "settings": {
    "import/resolver": {
      "typescript": {
        "alwaysTryTypes": true,
        "project": "./tsconfig.json"
      },
      "node": {
        "extensions": [".js", ".jsx", ".ts", ".tsx", ".vue"],
        "moduleDirectory": ["node_modules", "src"]
      },
      "alias": {
        "map": [["@", "./src"]],
        "extensions": [".ts", ".vue"]
      }
    }
  },
  "extends": [
    "@cs/eslint-config/eslintrc.vue3.js",
    "@cs/eslint-config/eslintrc.typescript-vue3.js"
  ],
  "rules": {
    "@typescript-eslint/no-unused-vars": "off",
    "no-console": "off",
    "no-debugger": "error",
    "vue/multi-word-component-names": "off",
    "import/prefer-default-export": "off",
    "no-shadow": "off",
    "no-param-reassign": ["off"],
    "@typescript-eslint/no-shadow": ["error"],
    "@typescript-eslint/no-unused-expressions": [2, { "allowTernary": true }],
    "@typescript-eslint/no-use-before-define": "off",
    "vue/no-unused-vars": ["off"],
    "import/no-cycle": [0, { "maxDepth": "∞" }],
    "consistent-return": "off",
    "no-void": "off",
    "radix": "off"
  }
}
