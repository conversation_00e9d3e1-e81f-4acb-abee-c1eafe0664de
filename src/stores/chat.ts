import { defineStore } from 'pinia';
import { AnswerStatusEnum } from '@/constants/chat';

export const useChatStore = defineStore('chat', {
  state: (): IChatSate => ({
    conversationInfo: {} as IConversation,
    answerStatus: AnswerStatusEnum.SUCCESS,
  }),

  actions: {
    setConversationInfo(val: IConversation) {
      this.conversationInfo = val;
    },
    setAnswerStatus(val: AnswerStatusEnum) {
      this.answerStatus = val;
    },
  },
});
