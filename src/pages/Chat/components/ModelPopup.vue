<template>
  <div v-if="show" class="model-card-popup">
    <div class="model-card-mask" @click="handleClose"></div>
    <div
      class="model-card-content"
      :class="{ 'voice-mode': inputMode === 'voice', 'keyboard-mode': inputMode === 'keyboard' }"
    >
      <div class="model-list">
        <div
          v-for="(model, key) in modelList"
          :key="key"
          class="model-item"
          :class="{ active: selectedModel === model.model }"
          @click="handleSelectModel(model.model, model.label, model.imageUrl)"
        >
          <div class="model-item-content">
            <span class="model-name">{{ model.label }}</span>
            <img v-if="model.imageUrl" :src="model.imageUrl" alt="模型图标" class="model-icon" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';

// 模型数据类型定义
interface IModelInfo {
  model: string;
  label: string;
  description: string;
  maxTokenCount: number;
  imageUrl: string;
}

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  modelList: {
    type: Object,
    default: () => ({}),
  },
  selectedModel: {
    type: String,
    default: '',
  },
  inputMode: {
    type: String,
    default: 'keyboard',
    validator: (value: string) => ['keyboard', 'voice'].includes(value),
  },
});

const emit = defineEmits(['close', 'select-model']);

const handleClose = () => {
  emit('close');
};

const handleSelectModel = (modelName: string, modelLabel: string, imageUrl?: string) => {
  emit('select-model', { modelName, modelLabel, imageUrl });
  // 选择模型后自动关闭弹窗
  emit('close');
};
</script>

<style lang="scss" scoped>
// 模型选择卡片样式
.model-card-popup {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2000;
  display: flex;
  flex-direction: column;
  pointer-events: none;

  .model-card-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(3px);
    pointer-events: auto;
  }

  .model-card-content {
    position: absolute;
    width: 380px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(250, 250, 250, 0.95) 100%);
    backdrop-filter: blur(15px);
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.1);
    pointer-events: auto;

    &.keyboard-mode {
      bottom: 150px;
      right: 100px;
    }

    &.voice-mode {
      bottom: 220px;
      right: 50px;
    }
  }
}

.model-list {
  padding: 16px;
  max-height: 500px;
  overflow-y: auto;

  .model-item {
    margin-bottom: 12px;
    border-radius: 16px;
    background: rgba(240, 240, 240, 0.8);
    border: 1px solid rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.2s;
    overflow: hidden;

    &:last-child {
      margin-bottom: 0;
    }

    &.active {
      background: rgba(220, 220, 220, 0.9);
      border: 2px solid #55b0a5;
      box-shadow: 0 0 12px rgba(85, 176, 165, 0.3);
    }

    &:hover {
      transform: translateY(-2px);
      background: rgba(230, 230, 230, 0.9);
      border-color: rgba(0, 0, 0, 0.2);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }
    .model-item-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      .model-name {
        font-size: 20px;
        font-weight: 600;
        color: rgba(51, 51, 51, 0.9);
      }
      .model-icon {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        object-fit: cover;
      }
    }
  }
}
</style>
