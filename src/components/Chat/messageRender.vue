<template>
  <div class="markdown-body custom-markdown-body" @click="handleClick" v-html="mdText"></div>
  <QRCodePopup v-model:show="showQRCode" :url="qrCodeUrl" />
</template>

<script lang="ts" setup>
import { computed, ref, onMounted, onUnmounted, reactive } from 'vue';
import MarkdownIt from 'markdown-it';
import markdownItLinkAttributes from 'markdown-it-link-attributes';
import markdownItFootnote from 'markdown-it-footnote';
import hljs from 'highlight.js/lib/core';
import javascript from 'highlight.js/lib/languages/javascript';
import 'highlight.js/styles/github.css';
import 'github-markdown-css/github-markdown-light.css';
import '@/styles/markdown.scss';
import QRCodePopup from '@/components/QRCode/QRCodePopup.vue';

hljs.registerLanguage('javascript', javascript);

const props = defineProps({
  text: {
    type: String,
    required: true,
  },
});

// 添加视频标记解析规则
const videoRule = (md: MarkdownIt) => {
  const videoRegex = /\[video\]\((.*?)\)/;
  md.core.ruler.after('inline', 'video', (state) => {
    const { tokens } = state;
    for (let i = 0; i < tokens.length; i++) {
      if (tokens[i].type === 'inline') {
        const { content } = tokens[i];
        const match = content.match(videoRegex);
        if (match) {
          const videoUrl = match[1];
          tokens[i].type = 'html_block';
          tokens[i].content = `
            <video controls class="markdown-video">
              <source src="${videoUrl}" type="video/mp4">
              您的浏览器不支持视频标签
            </video>
          `;
        }
      }
    }
    return true;
  });
};

// 在创建 markdown 实例时添加自定义规则
const md = new MarkdownIt({
  html: true,
  breaks: true,
  highlight: (str: string, lang: string) => {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return renderCode(hljs.highlight(str, { language: lang, ignoreIllegals: true }).value);
      } catch (error) {
        console.error('no such language supported', error);
        // window.Owl.addError(
        //   { name: 'md_error', msg: error },
        //   {
        //     level: 'info',
        //     category: 'md_error',
        //   },
        // );
      }
    }
    return renderCode(md.utils.escapeHtml(str));
  },
});

md.use(videoRule); // 添加视频规则
md.use(markdownItFootnote);

interface ILinkAttributesOptions {
  pattern: RegExp;
  attrs: {
    target: string;
    rel: string;
  };
}

md.use(markdownItLinkAttributes, {
  pattern: /^(https?|ftp):/,
  attrs: {
    target: '_blank',
    rel: 'noopener noreferrer',
  },
} as ILinkAttributesOptions);

// 定义代码渲染函数
function renderCode(originalCode: string): string {
  return `<pre class="hljs hljs-copy-wrapper"><code>${originalCode}</code></pre>`;
}

// 计算属性，将 markdown 文本转换为 HTML
const mdText = computed(() => md.render(props.text));

// 检测是否为移动设备
const isMobileDevice = () => {
  // 只通过User Agent判断真正的移动设备，不依赖窗口宽度
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

// QR Code popup state
const showQRCode = ref(false);
const qrCodeUrl = ref('');

// 点击处理函数
const handleClick = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  // Handle link clicks
  if (target.tagName === 'A') {
    const url = target.getAttribute('href');
    if (url) {
      // 在移动设备上直接跳转链接
      if (isMobileDevice()) {
        // 不阻止默认行为，让链接正常打开
        return;
      }
      // 在桌面设备上显示二维码
      event.preventDefault();
      qrCodeUrl.value = url;
      showQRCode.value = true;
    }
  }
  // Handle question text clicks (if needed)
  if (target.classList.contains('q-text')) {
    const text = target.innerText;
    // emit('sendQuestion', text);
  }
};
</script>

<style lang="scss" scoped></style>
