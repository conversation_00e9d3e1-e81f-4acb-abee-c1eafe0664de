const ora = require('ora');
const path = require('path');
const process = require('process');
const Webpack = require('webpack');
const Merge = require('webpack-merge');
const BaseConfig = require('./config.js');
const TerserPlugin = require('terser-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
const f2eci = require('../f2eci.json');

function resolve(dir) {
  return path.resolve(process.cwd(), dir);
}
/**
 * @type {Webpack.Configuration}
 */
const ProductionConfig = Merge.merge(BaseConfig, {
  mode: 'production',
  entry: {
    index: './src/main.ts',
  },
  optimization: {
    minimize: true,
    minimizer: [new TerserPlugin({
      extractComments: false,
      parallel: 2,
    })],
    splitChunks: {
      chunks: 'all',
      minSize: 80000,
      maxSize: 250000,
      minChunks: 1,
      maxAsyncRequests: 30,
      maxInitialRequests: 30,
      cacheGroups: {
        vendors: {
          test: /[\\/]node_modules[\\/]/,
          priority: -10,
          reuseExistingChunk: true,
        },
        async: {
          minChunks: 2,
          priority: -20,
          reuseExistingChunk: true,
          chunks: 'async',
        },
        common: {
          minChunks: 2,
          priority: -20,
          reuseExistingChunk: true,
          chunks: 'initial',
        },
      },
    },
  },
  plugins: [
    new HtmlWebpackPlugin({
      template: resolve('public/index.html'),
      filename: 'index.html',
      inject: true,
    }),
    new MiniCssExtractPlugin({
      filename: 'css/[name].[contenthash].css',
    }),
    new Webpack.DefinePlugin({
      'process.env': {
        VUE_APP_ENV: JSON.stringify(process.env.VUE_APP_ENV),
      },
    }),
    process.env.ANALYZE ? new BundleAnalyzerPlugin() : '',
  ].filter((item) => !!item),
});

const spinner = ora('正在构建');

function start (config) {
  spinner.start()
  Webpack(ProductionConfig, callback)
}

function callback (err, stats) {
  
  if (err) {
    console.error(err.stack || err);
    spinner.error('构建失败');
    process.exit(1)
  }
  
  process.stdout.write(`${stats.toString({
    colors: true,
    modules: false,
    children: false,
    chunks: false,
    chunkModules: false,
  })}\n\n`)
  if (stats.hasErrors()) {
    spinner.fail('构建失败');
    process.exit(1);
  } else {
    spinner.succeed('构建完成');
  }
};

start(ProductionConfig);

