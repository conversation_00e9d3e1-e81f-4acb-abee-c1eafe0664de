{"presets": ["@babel/preset-env", ["@babel/preset-typescript", {"allExtensions": true}]], "plugins": ["@babel/plugin-transform-typescript", "@babel/plugin-transform-runtime", "@babel/plugin-proposal-class-properties", "@babel/plugin-syntax-dynamic-import", "@babel/plugin-proposal-private-methods", "@babel/plugin-proposal-nullish-coalescing-operator", "@babel/plugin-proposal-optional-chaining", ["@babel/plugin-proposal-decorators", {"legacy": true}], "transform-vue-jsx"]}