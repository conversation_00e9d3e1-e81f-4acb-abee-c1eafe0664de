import { defineStore } from 'pinia';
import { getUserInfo } from '@/apis/common';

export const useCommonStore = defineStore('commonData', {
  state: () => ({
    userData: {},
    activeMenu: '',
    userIcon: 'https://serverless.sankuai.com/dx-avatar/?type=img&mis=',
    name: '',
  }),
  actions: {
    async getUserInfoAction() {
      const { data } = await getUserInfo();

      this.userData = data ?? {};
      this.userIcon += data?.login ?? '';
      this.name = data?.name ?? '';
    },
  },
});
