<template>
  <div class="loading-tip">
    <div class="custom-loader">
      <div class="shape" :class="'shape-' + currentShapeIndex"></div>
    </div>
    <div class="loading-text-container">
      <transition name="fade" mode="out-in">
        <span :key="currentTextIndex" class="loading-text">
          {{ loadingTexts[currentTextIndex] }}
          <span class="dots">
            <span class="dot dot1">.</span>
            <span class="dot dot2">.</span>
            <span class="dot dot3">.</span>
          </span>
        </span>
      </transition>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';

// 加载提示文字轮播
const loadingTexts = [
  '组织语言中',
  '分析您的画像中',
  '查询相关资料中',
  '连接知识库中',
  '检索最新信息中',
  '计算最佳答案中',
  '分析您的偏好中',
  '处理您的请求中',
  '为您思考中',
  '查找最新资讯中',
  '分析您的问题中',
  '连接服务器中',
  '寻找最佳答案中',
  '整合多方信息中',
];

// 已显示过的文字索引记录
const usedTextIndices = ref<number[]>([]);
const currentTextIndex = ref(0);
const currentShapeIndex = ref(0);
const loadingInterval = ref<number | null>(null);
const shapeInterval = ref<number | null>(null);

// 获取随机且不重复的文字索引
const getRandomTextIndex = () => {
  // 如果所有文字都已经显示过，则重置记录
  if (usedTextIndices.value.length >= loadingTexts.length - 1) {
    usedTextIndices.value = [currentTextIndex.value];
  }
  let newIndex;
  do {
    newIndex = Math.floor(Math.random() * loadingTexts.length);
  } while (usedTextIndices.value.includes(newIndex));
  usedTextIndices.value.push(newIndex);
  return newIndex;
};

onMounted(() => {
  // 初始化第一个文字索引
  currentTextIndex.value = Math.floor(Math.random() * loadingTexts.length);
  usedTextIndices.value.push(currentTextIndex.value);
  // 启动文字轮播
  loadingInterval.value = window.setInterval(() => {
    currentTextIndex.value = getRandomTextIndex();
  }, 1500); // 从2000ms减少到1500ms
  // 启动形状变换
  shapeInterval.value = window.setInterval(() => {
    currentShapeIndex.value = (currentShapeIndex.value + 1) % 4; // 4种形状循环
  }, 500); // 从600ms减少到500ms
  // 使用CSS动画代替JavaScript操作DOM
});

onUnmounted(() => {
  // 清除定时器
  if (loadingInterval.value) {
    clearInterval(loadingInterval.value);
  }
  if (shapeInterval.value) {
    clearInterval(shapeInterval.value);
  }
  // 不需要清除dotsInterval，因为我们使用CSS动画
});
</script>

<style scoped lang="scss">
.loading-tip {
  display: inline-flex;
  align-items: center;
  font-size: 24px;
  font-weight: 650;
  font-family: 'Trebuchet MS', 'Lucida Sans Unicode', 'Lucida Grande', 'Lucida Sans', Arial, sans-serif;
  color: var(--text-primary);
  background: var(--bg-glass);
  border: 2px solid var(--border-accent);
  padding: 16px 48px 16px 16px;
  margin: 16px 0;
  border-radius: 48px;
  width: auto;
  max-width: 80%;
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-strong), var(--shadow-accent);
  position: relative;
  white-space: nowrap;
  letter-spacing: 0.5px;
  z-index: 1;
  animation: tipFade 2s ease-in-out infinite;
  .custom-loader {
    width: 40px;
    height: 40px;
    margin-right: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .shape {
    width: 24px;
    height: 24px;
    transition: all 0.3s ease;
    background-color: var(--accent-color);
  }
  .shape-0 {
    border-radius: 50%;
    transform: scale(1);
    box-shadow: 0 0 8px var(--accent-color-medium);
  }
  .shape-1 {
    border-radius: 4px;
    transform: rotate(45deg) scale(0.8);
    box-shadow: 0 0 8px var(--accent-color-medium);
  }
  .shape-2 {
    width: 24px;
    height: 24px;
    background-color: transparent;
    position: relative;
    &:before,
    &:after {
      content: '';
      position: absolute;
      width: 14px;
      height: 14px;
      border-radius: 50%;
      background-color: var(--accent-color);
      top: 50%;
      transform: translateY(-50%);
      box-shadow: 0 0 6px var(--accent-color-medium);
    }
    &:before {
      left: -5px;
    }
    &:after {
      right: -5px;
    }
  }
  .shape-3 {
    width: 8px;
    height: 24px;
    border-radius: 4px;
    transform: scale(1) rotate(0deg);
    background-color: var(--accent-color);
    box-shadow:
      12px 0 0 0 var(--accent-color-strong),
      -12px 0 0 0 var(--accent-color-medium),
      0 0 8px var(--accent-color-medium);
  }
  .loading-text-container {
    display: flex;
    align-items: center;
    min-height: 36px; // 确保高度固定，防止文字切换时高度变化
    white-space: nowrap;
    flex: 1;
    .loading-text {
      display: inline-block;
      .dots {
        display: inline-block;
        margin-left: 2px;
        .dot {
          display: inline-block;
          opacity: 0.3;
          animation: dotPulse 0.8s infinite;
          color: var(--accent-color);
        }
        .dot1 {
          animation-delay: 0s;
        }
        .dot2 {
          animation-delay: 0.25s;
        }
        .dot3 {
          animation-delay: 0.5s;
        }
      }
    }
    @keyframes dotPulse {
      0%,
      100% {
        opacity: 0.3;
      }
      50% {
        opacity: 1;
      }
    }
    // 文字淡入淡出效果
    .fade-enter-active,
    .fade-leave-active {
      transition:
        opacity 0.3s ease,
        transform 0.3s ease;
    }
    .fade-enter-from {
      opacity: 0;
      transform: translateY(10px);
    }
    .fade-leave-to {
      opacity: 0;
      transform: translateY(-10px);
    }
  }
}

@keyframes tipFade {
  0% {
    opacity: 0.9;
    transform: translateY(0);
  }
  50% {
    opacity: 1;
    transform: translateY(-3px);
  }
  100% {
    opacity: 0.9;
    transform: translateY(0);
  }
}
</style>
