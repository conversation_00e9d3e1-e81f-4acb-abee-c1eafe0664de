<template>
  <div class="add-reminder-card" @click="handleClick">
    <div class="add-reminder-content">
      <div class="add-icon">
        <p class="add">+</p>
      </div>
      <div class="add-text">添加提醒</div>
    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits<{
  click: [];
}>();

const handleClick = () => {
  emit('click');
};
</script>

<style scoped lang="scss">
.add-reminder-card {
  width: 220px;
  height: 220px;
  background: rgba(1, 28, 32, 0.4);
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  border-radius: 16px;
  padding: 20px;
  box-sizing: border-box;
  transition: all 0.3s ease;
  cursor: pointer;
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON>o, sans-serif;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 12px 32px rgba(0, 255, 255, 0.2);
    border-color: #00ffff;
    background: rgba(1, 28, 32, 0.6);
    border-style: solid;
  }

  &:active {
    transform: translateY(-4px) scale(1.02);
  }
}

.add-reminder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  text-align: center;
}

.add-icon {
  width: 60px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 255, 255, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease;

  .add {
    color: rgba(255, 255, 255, 0.8);
    font-size: 48px; // 增加8px (原来32px)
    font-weight: bold;
    transition: all 0.3s ease;
  }
}

.add-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28px; // 增加4px (原来16px)
  font-weight: 500;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.add-reminder-card:hover {
  .add-icon {
    background: rgba(0, 255, 255, 0.2);
    transform: scale(1.1);

    .add-image {
      opacity: 1;
      filter: brightness(0) invert(1) sepia(1) saturate(10000%) hue-rotate(180deg);
    }
  }

  .add-text {
    color: #00ffff;
    transform: translateY(-2px);
  }
}
</style>
