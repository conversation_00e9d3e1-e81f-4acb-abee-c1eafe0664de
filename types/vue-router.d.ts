/* eslint-disable @typescript-eslint/naming-convention */
// This can be directly added to any of your `.ts` files like `router.ts`
// It can also be added to a `.d.ts` file. Make sure it's included in
// project's tsconfig.json "files"
import 'vue-router';

// To ensure it is treated as a module, add at least one `export` statement
export {};
declare module 'vue-router' {
  interface RouteMeta {
    cid?: string;
    title?: string;
    component?: string;
    pageCase?: (type: string, bid: string, customData?: Record<string, unknown> | null) => void;
  }
}
