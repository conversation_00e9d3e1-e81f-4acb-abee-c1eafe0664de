// 头像相关工具函数

/**
 * 根据用户名生成一致的随机颜色
 * @param username 用户名或唯一标识
 * @returns 颜色字符串（如 #AABBCC）
 */
export function getRandomColor(username: string): string {
  if (!username) return '#4ecdc4';
  // 简单哈希：将每个字符的charCode累加，避免位运算
  let hash = 0;
  for (let i = 0; i < username.length; i++) {
    hash += username.charCodeAt(i) * (i + 1);
  }
  // HSL生成明亮鲜艳的颜色，色相分布均匀，饱和度和亮度高
  const hue = hash % 360; // 色相 0-359
  const saturation = 60 + (hash % 13); // 饱和度 60-72%
  const lightness = 62 + (hash % 9); // 亮度 62-70%

  // HSL转RGB
  function hslToRgb(h: number, s: number, l: number) {
    s /= 100;
    l /= 100;
    const k = (n: number) => (n + h / 30) % 12;
    const a = s * Math.min(l, 1 - l);
    const f = (n: number) => {
      const color = l - a * Math.max(-1, Math.min(Math.min(k(n) - 3, 9 - k(n)), 1));
      return Math.round(255 * color);
    };
    return [f(0), f(8), f(4)];
  }
  const [r, g, b] = hslToRgb(hue, saturation, lightness);
  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
}

/**
 * 获取用户名首字母（大写）
 * @param username 用户名
 * @returns 首字母
 */
export function getAvatarLetter(username: string): string {
  if (!username) return '';
  return username.charAt(0).toUpperCase();
}
