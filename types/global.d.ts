declare module '*.scss';
declare module '*.png';
declare module '*.svg';
declare module '*.jpeg';
declare module '*.jpg';
declare module 'recorder-realtime';
declare module '*.vue' {
  import type { DefineComponent } from 'vue';

  const component: DefineComponent<{}, {}, any>;
  export default component;
}

/* eslint-disable */
interface Window {
  webkitAudioContext: typeof AudioContext;
  owl: any;
}
