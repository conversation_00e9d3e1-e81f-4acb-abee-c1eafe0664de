import { RecordRTCPromisesHandler } from 'recordrtc';

class RecorderSingleton {
  private static instance: RecorderSingleton;

  private stream: MediaStream | null = null;

  // eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents, @typescript-eslint/no-explicit-any
  private recorder: any | null = null;

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() {}

  public static getInstance(): RecorderSingleton {
    if (!RecorderSingleton.instance) {
      RecorderSingleton.instance = new RecorderSingleton();
    }
    return RecorderSingleton.instance;
  }

  public async init(mediaType: 'video' | 'audio' = 'video'): Promise<void> {
    const constraints = mediaType === 'audio' ? { audio: true } : { video: true, audio: true };
    this.stream = await navigator.mediaDevices.getUserMedia(constraints);
    this.recorder = new RecordRTCPromisesHandler(this.stream, {
      type: mediaType,
    });
  }

  public async startRecording(): Promise<void> {
    if (this.recorder) {
      await this.recorder.startRecording();
    } else {
      console.error('Recorder not initialized.');
    }
  }

  public async stopRecording(): Promise<Blob | null> {
    if (this.recorder) {
      await this.recorder.stopRecording();
      const blob = await this.recorder.getBlob();
      await this.destroy();
      return blob;
    }
    console.error('Recorder not initialized.');
    return null;
  }

  // 取消录音
  public async cancelRecording(): Promise<void> {
    if (this.recorder) {
      // 直接停止录制但不获取 blob
      await this.recorder.stopRecording();
      // 清理资源
      await this.destroy();
    } else {
      console.error('Recorder not initialized.');
    }
  }

  public async destroy(): Promise<void> {
    if (this.recorder) {
      await this.recorder.destroy();
      this.recorder = null;
    }
    if (this.stream) {
      this.stream.getTracks().forEach((track) => track.stop());
      this.stream = null;
    }
  }

  public invokeSaveAsDialog(blob: Blob): void {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = 'recording.webm';
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
  }

  public static sleep(m: number): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, m);
    });
  }
}

export default RecorderSingleton.getInstance();
