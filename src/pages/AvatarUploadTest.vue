<template>
  <div class="avatar-test-container">
    <div class="test-header">
      <h1>头像上传功能测试</h1>
      <p>测试节点头像上传功能</p>
    </div>

    <div class="test-content">
      <div class="test-section">
        <h2>基础头像上传</h2>
        <div class="avatar-demo">
          <AvatarUpload
            v-model="avatarUrl1"
            :size="80"
            placeholder="上传头像"
            :max-size="10"
            @upload-success="onUploadSuccess"
            @upload-error="onUploadError"
          />
          <div class="avatar-info">
            <p>当前头像URL: {{ avatarUrl1 || '未上传' }}</p>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h2>不同尺寸的头像上传</h2>
        <div class="avatar-sizes">
          <div class="size-demo">
            <h3>小尺寸 (60px)</h3>
            <AvatarUpload v-model="avatarUrl2" :size="60" placeholder="小头像" :max-size="10" />
          </div>
          <div class="size-demo">
            <h3>中等尺寸 (100px)</h3>
            <AvatarUpload v-model="avatarUrl3" :size="100" placeholder="中头像" :max-size="10" />
          </div>
          <div class="size-demo">
            <h3>大尺寸 (120px)</h3>
            <AvatarUpload v-model="avatarUrl4" :size="120" placeholder="大头像" :max-size="10" />
          </div>
        </div>
      </div>

      <div class="test-section">
        <h2>禁用状态</h2>
        <div class="avatar-demo">
          <AvatarUpload v-model="avatarUrl5" :size="80" placeholder="禁用状态" :max-size="10" :disabled="true" />
        </div>
      </div>

      <div class="test-section">
        <h2>自定义颜色</h2>
        <div class="avatar-demo">
          <AvatarUpload
            v-model="avatarUrl6"
            :size="80"
            placeholder="自定义颜色"
            placeholder-color="#ff6b9d"
            :max-size="10"
          />
        </div>
      </div>
    </div>

    <div class="test-logs">
      <h2>操作日志</h2>
      <div class="logs-container">
        <div v-for="(log, index) in logs" :key="index" class="log-item" :class="log.type">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import AvatarUpload from '@/components/AvatarUpload.vue';

// 头像URL状态
const avatarUrl1 = ref('');
const avatarUrl2 = ref('');
const avatarUrl3 = ref('');
const avatarUrl4 = ref('');
const avatarUrl5 = ref('');
const avatarUrl6 = ref('');

// 日志记录
interface ILog {
  time: string;
  message: string;
  type: 'success' | 'error' | 'info';
}

const logs = ref<ILog[]>([]);

const addLog = (message: string, type: ILog['type'] = 'info') => {
  const now = new Date();
  const time = now.toLocaleTimeString();
  logs.value.unshift({ time, message, type });

  // 限制日志数量
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50);
  }
};

// 上传成功处理
const onUploadSuccess = (url: string) => {
  addLog(`头像上传成功: ${url}`, 'success');
};

// 上传失败处理
const onUploadError = (error: string) => {
  addLog(`头像上传失败: ${error}`, 'error');
};

// 初始化日志
addLog('头像上传测试页面已加载', 'info');
</script>

<style lang="scss" scoped>
.avatar-test-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  padding: 20px;
  color: white;
}

.test-header {
  text-align: center;
  margin-bottom: 40px;

  h1 {
    font-size: 32px;
    margin-bottom: 10px;
    color: #00ffff;
  }

  p {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
  }
}

.test-content {
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 30px;
  margin-bottom: 30px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);

  h2 {
    font-size: 24px;
    margin-bottom: 20px;
    color: #00ffff;
  }

  h3 {
    font-size: 18px;
    margin-bottom: 15px;
    color: rgba(255, 255, 255, 0.9);
  }
}

.avatar-demo {
  display: flex;
  align-items: center;
  gap: 30px;

  .avatar-info {
    flex: 1;

    p {
      margin: 0;
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
      word-break: break-all;
    }
  }
}

.avatar-sizes {
  display: flex;
  gap: 40px;
  flex-wrap: wrap;

  .size-demo {
    text-align: center;
  }
}

.test-logs {
  max-width: 1200px;
  margin: 40px auto 0;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 16px;
  padding: 30px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);

  h2 {
    font-size: 24px;
    margin-bottom: 20px;
    color: #00ffff;
  }
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

.log-item {
  display: flex;
  gap: 15px;
  padding: 8px 12px;
  margin-bottom: 5px;
  border-radius: 8px;
  font-size: 14px;

  &.success {
    background: rgba(0, 255, 0, 0.1);
    border-left: 3px solid #00ff00;
  }

  &.error {
    background: rgba(255, 0, 0, 0.1);
    border-left: 3px solid #ff0000;
  }

  &.info {
    background: rgba(0, 255, 255, 0.1);
    border-left: 3px solid #00ffff;
  }

  .log-time {
    color: rgba(255, 255, 255, 0.6);
    font-family: monospace;
    flex-shrink: 0;
  }

  .log-message {
    color: rgba(255, 255, 255, 0.9);
    word-break: break-all;
  }
}

@media (max-width: 768px) {
  .avatar-sizes {
    flex-direction: column;
    align-items: center;
  }

  .avatar-demo {
    flex-direction: column;
    text-align: center;
  }
}
</style>
