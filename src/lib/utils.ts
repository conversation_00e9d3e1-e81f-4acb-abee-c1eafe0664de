/**
 * 时长
 * @param time
 */
export function parseTimeData(time: number) {
  const SECOND = 1000;
  const MINUTE = 60 * SECOND;
  const HOUR = 60 * MINUTE;
  const DAY = 24 * HOUR;

  if (time === -1 || time === 0) {
    return '-';
  }

  const days = Math.floor(time / DAY);
  const hours = Math.floor((time % DAY) / HOUR);
  const minutes = Math.floor((time % HOUR) / MINUTE);
  const seconds = Math.floor((time % MINUTE) / SECOND);

  let str = '';

  function padZero(num: number, targetLength = 2) {
    let str1 = `${num}`;

    while (str1.length < targetLength) {
      str1 = str1.padStart(targetLength, '0');
    }

    return str1;
  }

  if (days > 0) {
    str += `${padZero(days)}天`;
  }

  if (hours > 0) {
    str += `${padZero(hours)}小时`;
  }

  if (minutes > 0) {
    str += `${padZero(minutes)}分`;
  }

  str += `${padZero(seconds)}秒`;

  return str;
}

export function getSecondLevelPath() {
  const url = window.location.pathname;
  const regex = /^\/([^/]+)\/([^/]+)/;
  const regexSecond = /^\/([^/]+)\/(.+)/;
  const match = url.match(regex);
  const matchSecond = url.match(regexSecond);

  if (match && match[0] === '/ml/managementCenter') {
    return matchSecond && matchSecond[2] ? matchSecond[2] : '';
  }
  return match && match.length >= 3 ? match[2] : '';
}
