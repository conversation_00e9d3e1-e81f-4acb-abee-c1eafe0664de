import { createApp } from 'vue';
import { createPinia } from 'pinia';
import { useCommonStore } from '@/stores/common';
import SSOWeb from '@/lib/sso';
import { getUserInfo } from '@/apis/common';
import router from '@/router/index';
import instance from '@/lib/axios';
import '@/lib/owl';
import '@/styles/index.scss';
import './style.scss';
import 'vant/lib/index.css';
import '@vant/touch-emulator';
import fetchInstance from '@/lib/fetch';
import App from './App.vue';

const pinia = createPinia();
const app = createApp(App);
SSOWeb.login()
  .then(async (ssoid: unknown) => {
    if (ssoid && typeof ssoid === 'string') {
      instance.defaults.headers.common['access-token'] = ssoid;
      fetchInstance.setAccessToken(ssoid);
      const userData = await getUserInfo();
      window.owl('setDimension', {
        unionId: userData.login,
      });
      app.use(pinia);
      app.use(router);
      app.mount('#app');
    }
  })
  .catch((err) => {
    console.log(err);
  });
