<template>
  <div class="xiaomei-item" @click="handleClick">
    <div class="xiaomei-item-content">
      <div class="xiaomei-item-icon">{{ icon }}</div>
      <div class="xiaomei-item-title">{{ title }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';

// 定义props
defineProps<{
  title: string;
  icon: string;
}>();

// 定义emits
const emit = defineEmits<{
  click: [];
}>();

// 处理点击事件
const handleClick = () => {
  emit('click');
};
</script>

<style lang="scss" scoped>
.xiaomei-item {
  background: rgba(113, 229, 247, 0.15); // 比container更浅的透明度（从0.6改为0.3）
  border: 2px solid rgba(0, 255, 255, 0.3);
  border-radius: 16px;
  padding: 24px; // 增加内边距
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 0 8px rgba(0, 255, 255, 0.2);

  &:hover {
    border-color: rgba(0, 255, 255, 0.4);
    box-shadow: 0 0 16px rgba(0, 255, 255, 0.3);
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
  }
}

.xiaomei-item-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 100%;
  min-height: 120px; // 增加最小高度（从80px改为120px）
}

.xiaomei-item-icon {
  font-size: 48px; // 进一步增加图标大小（从40px改为48px）
  margin-bottom: 16px; // 增加间距
  line-height: 1;
}

.xiaomei-item-title {
  color: var(--text-primary);
  font-size: 26px; // 进一步增加字体大小（从22px改为26px）
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  line-height: 1.2;
}
</style>
