export class Typewriter {
  private queue: string[] = [];

  private consuming = false;

  private timer: ReturnType<typeof setTimeout>;

  private lastContent = ''; // 记录上次的完整内容

  private displayedContent = ''; // 当前已显示的内容

  private isFinished = false; // 标记是否已完成（收到isFinal标志）

  private onComplete?: () => void; // 完成回调

  private startTime = 0; // 记录开始时间

  private minDisplayTime = 200; // 最小显示时间（毫秒），确保短消息也有打字机效果

  private allowAutoComplete = false; // 是否允许自动完成（只有收到isFinal后才允许）

  constructor(
    private onConsume: (str: string) => void,
    onComplete?: () => void,
  ) {
    this.onComplete = onComplete;
  }

  // 优化的动态速度：确保短消息也有良好的打字机效果
  dynamicSpeed() {
    // 如果已标记完成，使用较快速度来消费剩余队列
    if (this.isFinished) {
      return 25; // 完成后使用25ms速度，稍微慢一点以保持打字机效果
    }

    // 基础速度：35ms（适中的速度，确保用户能看到打字机效果）
    const baseSpeed = 35;

    // 如果队列为空，使用基础速度
    if (this.queue.length === 0) {
      return baseSpeed;
    }

    // 根据队列长度动态调整速度
    if (this.queue.length > 20) {
      return 20; // 队列很长时使用快速度
    }
    if (this.queue.length > 10) {
      return 25; // 队列较长时使用中等速度
    }
    if (this.queue.length <= 3) {
      return 50; // 队列很短时使用慢速度，确保打字机效果可见
    }

    return baseSpeed;
  }

  // 添加字符串到队列（处理累积文本）
  add(str: string) {
    if (!str) return;

    // 如果新内容和上次内容相同，跳过
    if (str === this.lastContent) {
      return;
    }

    // 如果新内容是上次内容的扩展，只添加增量部分
    if (str.startsWith(this.lastContent)) {
      const incrementalText = str.substring(this.lastContent.length);
      if (incrementalText) {
        this.queue.push(incrementalText);
      }
    } else if (str.startsWith(this.displayedContent)) {
      // 如果新内容包含了当前已显示的内容，只添加新增部分
      const incrementalText = str.substring(this.displayedContent.length);
      if (incrementalText) {
        // 修复：不清空队列，而是追加新内容，避免显示跳跃
        // 检查队列中是否已经包含这部分内容，避免重复添加
        const queueContent = this.queue.join('');
        if (!queueContent.includes(incrementalText)) {
          this.queue.push(incrementalText);
        }
      }
    } else if (str.length > this.displayedContent.length) {
      // 如果新内容比当前显示内容长但不包含已显示内容
      // 可能是服务器重新发送，检查是否应该从当前位置继续
      const possibleIncrement = str.substring(this.displayedContent.length);
      if (possibleIncrement) {
        // 检查队列中是否已经包含这部分内容
        const queueContent = this.queue.join('');
        if (!queueContent.includes(possibleIncrement)) {
          this.queue.push(possibleIncrement);
        }
      }
    } else {
      // 完全不同的内容，这种情况下才重新开始
      console.warn('[Typewriter] 收到完全不同的内容，重新开始显示');
      this.queue = [str];
      this.displayedContent = '';
    }

    this.lastContent = str;
  }

  // 消费
  consume() {
    if (this.queue.length > 0) {
      const incrementalText = this.queue.shift();
      if (incrementalText) {
        // 累积显示的内容
        this.displayedContent += incrementalText;
        this.onConsume(this.displayedContent);
      }
    } else if (this.isFinished && this.allowAutoComplete && this.consuming) {
      // 队列消费完毕且已标记完成且允许自动完成，检查是否达到最小显示时间
      const elapsedTime = Date.now() - this.startTime;
      const remainingTime = Math.max(0, this.minDisplayTime - elapsedTime);

      if (remainingTime > 0) {
        console.log('[Typewriter] 队列消费完毕但未达到最小显示时间，延迟', remainingTime, 'ms后完成');
        setTimeout(() => {
          this.consuming = false;
          clearTimeout(this.timer);
          if (this.onComplete) {
            this.onComplete();
          }
        }, remainingTime);
      } else {
        console.log('[Typewriter] 队列消费完毕且已达到最小显示时间，立即完成');
        this.consuming = false;
        clearTimeout(this.timer);
        if (this.onComplete) {
          this.onComplete();
        }
      }
    } else if (this.queue.length === 0 && !this.allowAutoComplete) {
      // 队列为空但还未收到isFinal标志，继续等待
      console.log('[Typewriter] 队列为空但未收到isFinal标志，继续等待新数据');
    }
  }

  // 持续消费
  loopConsume() {
    this.consume();
    // 根据队列中字符的数量来设置消耗每一帧的速度，用定时器消耗
    this.timer = setTimeout(() => {
      if (this.consuming) {
        this.loopConsume();
      }
    }, this.dynamicSpeed());
  }

  // 开始消费队列
  start() {
    this.displayedContent = ''; // 重置已显示内容
    this.consuming = true;
    this.startTime = Date.now(); // 记录开始时间
    this.allowAutoComplete = false; // 重置自动完成标志
    this.loopConsume();
  }

  // 结束消费队列
  done() {
    this.consuming = false;
    clearTimeout(this.timer);

    // 修复：避免直接覆盖显示内容，保持渐进式显示
    if (this.queue.length > 0) {
      // 如果还有队列内容，快速消费完剩余队列而不是直接跳到最终内容
      while (this.queue.length > 0) {
        const incrementalText = this.queue.shift();
        if (incrementalText) {
          this.displayedContent += incrementalText;
        }
      }
      // 显示最终的累积内容
      this.onConsume(this.displayedContent);
    } else if (this.lastContent && this.lastContent !== this.displayedContent) {
      // 如果最终内容与已显示内容不同，但确保这是正确的最终状态
      this.onConsume(this.lastContent);
    }

    // 重置状态
    this.lastContent = '';
    this.displayedContent = '';
  }

  // 标记完成（收到isFinal标志，允许自动完成）
  markFinished() {
    this.isFinished = true;
    this.allowAutoComplete = true; // 收到isFinal后，允许自动完成
    console.log('[Typewriter] markFinished 调用，队列长度:', this.queue.length, '正在消费:', this.consuming);

    const elapsedTime = Date.now() - this.startTime;
    const remainingTime = Math.max(0, this.minDisplayTime - elapsedTime);

    // 如果队列为空，检查是否需要延迟完成以确保最小显示时间
    if (this.queue.length === 0) {
      if (remainingTime > 0) {
        console.log('[Typewriter] 队列为空但未达到最小显示时间，延迟', remainingTime, 'ms后完成');
        setTimeout(() => {
          this.consuming = false;
          clearTimeout(this.timer);
          if (this.onComplete) {
            this.onComplete();
          }
        }, remainingTime);
      } else {
        console.log('[Typewriter] 队列为空且已达到最小显示时间，立即完成');
        this.consuming = false;
        clearTimeout(this.timer);
        if (this.onComplete) {
          this.onComplete();
        }
      }
    } else {
      console.log('[Typewriter] 队列不为空，等待队列消费完毕后自动完成');
      // 队列不为空，继续消费，完成回调将在队列消费完毕后自动触发
    }
  }

  // 停止打印
  stop() {
    this.consuming = false;
    this.queue = [];
    this.lastContent = '';
    this.displayedContent = '';
    this.isFinished = false;
    this.allowAutoComplete = false; // 重置自动完成标志
  }
}
