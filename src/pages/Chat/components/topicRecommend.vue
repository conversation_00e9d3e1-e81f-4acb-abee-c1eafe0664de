<template>
  <!-- 中间内容区域 -->
  <div class="v-topicRecommend-content">
    <div class="title">
      <div class="title-one">
        <span class="sci-fi-text">人际关系助手</span>
        <div class="avatar-container">
          <img src="@/assets/img/xiaolian.png" alt="" />
          <div class="avatar-glow"></div>
        </div>
      </div>
      <div class="title-tow">
        <span class="sci-fi-text">探索你的</span>
        <span class="sci-fi-text highlight">社交圈</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits(['handle-have-try']);
</script>

<style scoped lang="scss">
.v-topicRecommend-content {
  width: 100%;
  height: 100%;
  padding: 20px 40px 30px 40px;
  margin: 60px 0px 0px 0px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  position: relative;

  .title {
    font-size: 54px;
    font-weight: 500;
    line-height: 70px;
    margin-top: 10px;
    position: relative;
    z-index: 10;

    .title-one {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      .sci-fi-text {
        color: #ffffff;
        margin-right: 20px;
      }

      .avatar-container {
        position: relative;
        display: inline-block;

        img {
          width: 96px;
          height: auto;
          filter: drop-shadow(0 0 15px rgba(0, 100, 255, 0.6));
          animation: avatarFloat 3s ease-in-out infinite;
        }

        .avatar-glow {
          position: absolute;
          top: -5px;
          left: -5px;
          right: -5px;
          bottom: -5px;
          background: radial-gradient(circle, rgba(0, 100, 255, 0.2) 0%, transparent 70%);
          border-radius: 50%;
          animation: glowPulse 2s ease-in-out infinite alternate;
        }
      }
    }

    .title-tow {
      .sci-fi-text {
        color: #ffffff;

        &.highlight {
          color: rgb(58, 220, 220);
        }
      }
    }
  }
}

// 动画定义

@keyframes avatarFloat {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes glowPulse {
  0% {
    opacity: 0.3;
    transform: scale(1);
  }
  100% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}
</style>
