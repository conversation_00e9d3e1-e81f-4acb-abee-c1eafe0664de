/**
 * 图片预处理工具函数
 * 用于处理用户头像，确保在关系图谱中正确显示
 */

/**
 * 将任意形状的图片处理成带白色背景的圆形图片
 * @param imageUrl 原始图片URL
 * @returns Promise<string> 处理后的base64图片URL
 */
export const processAvatarImage = (imageUrl: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous'; // 处理跨域问题

    img.onload = () => {
      try {
        // 创建canvas元素
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        if (!ctx) {
          reject(new Error('无法获取canvas上下文'));
          return;
        }

        // 设置canvas尺寸为正方形，取原图的最大边长
        const size = Math.max(img.width, img.height);
        canvas.width = size;
        canvas.height = size;

        // 创建圆形遮罩
        const radius = size / 2;
        const centerX = size / 2;
        const centerY = size / 2;

        // 保存当前状态
        ctx.save();

        // 创建圆形裁剪路径
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
        ctx.clip();

        // 在圆形裁剪区域内填充白色背景
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, size, size);

        // 计算图片在正方形画布中的居中位置
        const offsetX = (size - img.width) / 2;
        const offsetY = (size - img.height) / 2;

        // 在圆形裁剪区域内绘制图片
        ctx.drawImage(img, offsetX, offsetY, img.width, img.height);

        // 恢复状态
        ctx.restore();

        // 转换为base64格式
        const processedImageUrl = canvas.toDataURL('image/png');
        resolve(processedImageUrl);
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error(`图片加载失败: ${imageUrl}`));
    };

    img.src = imageUrl;
  });
};

/**
 * 批量处理多个头像图片
 * @param imageUrls 图片URL数组
 * @returns Promise<string[]> 处理后的图片URL数组
 */
export const processBatchAvatarImages = async (imageUrls: string[]): Promise<string[]> => {
  const promises = imageUrls.map((url) => processAvatarImage(url));
  return Promise.all(promises);
};

/**
 * 检查图片是否需要预处理
 * 如果图片已经是圆形或者是base64格式，可能不需要重复处理
 * @param imageUrl 图片URL
 * @returns boolean 是否需要预处理
 */
export const shouldProcessImage = (imageUrl: string): boolean => {
  // 如果已经是base64格式，可能已经处理过了
  if (imageUrl.startsWith('data:image/')) {
    return false;
  }

  // 其他情况都需要处理
  return true;
};
