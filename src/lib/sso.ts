import SSOWeb from '@mtfe/sso-web';
import config from '@/config/index';
/**
 * TODO 项目默认生成使用的是 MDP 项目线下的 clientId
 * 请业务后端先单独申请 SSO 资源，前端替换接口地址与 clientId
 * SSO 接入指南： @see {@link https://km.sankuai.com/page/13024494}
 * SSO 申请资源指南：@see {@link https://km.sankuai.com/page/368775561}
 * SSO 相关问题可咨询：suweijie02，songchao09，zhangshibo02
 */

export const ssoOption = {
  clientId: config.ssoClientId,
  accessEnv: config.ssoAccessEnv, // test or product
  // rewriteLocation: '',
  // callbackUrl: '/xiaomeivv',
};

export default SSOWeb(ssoOption);
