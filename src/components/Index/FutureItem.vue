<template>
  <div class="future-item" @click="handleClick">
    <div class="future-item-content">
      <div class="future-item-icon">{{ icon }}</div>
      <div class="future-item-title">{{ title }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';

// 定义props
defineProps<{
  title: string;
  icon: string;
}>();

// 定义emits
const emit = defineEmits<{
  showLockDialog: [];
}>();

// 处理点击事件
const handleClick = () => {
  emit('showLockDialog');
};
</script>

<style lang="scss" scoped>
.future-item {
  background: rgba(128, 128, 128, 0.2); // 灰色低透明度背景
  border: 2px solid rgba(128, 128, 128, 0.3);
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0 0 8px rgba(128, 128, 128, 0.2);
  opacity: 0.7; // 降低整体透明度表示未来功能
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    opacity: 0.8;
    transform: translateY(-2px);
    box-shadow: 0 0 12px rgba(128, 128, 128, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

.future-item-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 100%;
  min-height: 120px; // 与xiaomei-item统一 (100px -> 120px)
}

.future-item-icon {
  font-size: 48px; // 与xiaomei-item统一 (40px -> 48px)
  margin-bottom: 16px; // 与xiaomei-item统一 (12px -> 16px)
  line-height: 1;
  opacity: 0.8;
}

.future-item-title {
  color: rgba(255, 255, 255, 0.8);
  font-size: 26px; // 与xiaomei-item统一 (22px -> 26px)
  font-weight: 600; // 与xiaomei-item统一 (500 -> 600)
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  line-height: 1.2;
}
</style>
