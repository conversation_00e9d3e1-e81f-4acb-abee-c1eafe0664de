<template>
  <div class="app-wrapper">
    <div class="app-main">
      <!-- 二级路由出口 -->
      <router-view v-slot="{ Component, route }">
        <keep-alive>
          <component :is="Component" v-if="route.meta.keepAlive" :key="route.path" />
        </keep-alive>
        <component :is="Component" v-if="!route.meta.keepAlive" :key="route.path" />
      </router-view>
    </div>
  </div>
</template>
<script lang="ts" setup></script>

<style lang="scss" scoped>
.app-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: auto;

  .app-main {
    overflow: auto;
    background-color: $appBgColor;
  }
}
</style>
<style>
body {
  overflow: hidden;
}
</style>
