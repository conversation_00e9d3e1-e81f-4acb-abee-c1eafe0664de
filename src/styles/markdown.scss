.custom-markdown-body {
  background: transparent;
  word-break: break-all;
  font-size: inherit;


  ol li {
    list-style-type: decimal;
  }
  ul li {
    list-style-type: disc;
  }
  ul, ol {
    margin-bottom: 0;
    padding-left: 90px;
  }
  code {
    word-break: break-all !important;
    white-space: pre-wrap !important;
  }
  
  
  .hljs-copy-wrapper {
    background-color: #fff;
    padding: 8px;
    position: relative;
    overflow: hidden;

    &:hover {
      .hljs-copy-button {
        display: block;
      }
    }
  }

  .hljs-copy-button {
    display: none;
    position: absolute;
    right: 4px;
    top: 4px;
    font-size: 12px;
    color: #ffffff;
    background-color: #9999AA;
    padding: 2px 8px;
    border-radius: 4px;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05), 0 2px 4px rgba(0,0,0,0.05);
  }

  .markdown-video {
    width: 100%;
    max-width: 100%;
    height: auto;
  }
}
.markdown-body {
  // max-width: calc(100vw - 5rem);
  overflow: auto;
  p {
    margin-bottom: 0;
  }
  table {
    max-width: none;
  }
}
